@import "tailwindcss";

/* SVG CSS */

svg#bank-illustration:not(.animated) .animable {
  opacity: 0;
}
svg#bank-illustration.animated #freepik--background-simple--inject-4 {
  animation: 3s Infinite linear wind;
  animation-delay: 0s;
}
svg#bank-illustration.animated #freepik--Coins--inject-4 {
  animation: 3s Infinite linear floating;
  animation-delay: 0s;
}
@keyframes wind {
  0% {
    transform: rotate(0deg);
  }
  25% {
    transform: rotate(1deg);
  }
  75% {
    transform: rotate(-1deg);
  }
}
@keyframes floating {
  0% {
    opacity: 1;
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0px);
  }
}

/* Static background dots*/
.bg-static-dots {
  background-image: radial-gradient(
      circle,
      rgba(59, 130, 246, 0.2) 1.5px,
      transparent 1.5px
    ),
    radial-gradient(circle, rgba(147, 197, 253, 0.15) 1px, transparent 1px);
  background-size: 50px 50px, 35px 35px;
  background-position: 0 0, 25px 25px;
}

/* Glassmorphism effect */
.glass-card {
  background: rgba(30, 41, 59, 0.7);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(148, 163, 184, 0.1);
}

/* Button gradient effects */
.btn-gradient {
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
  transition: all 0.3s ease;
}

.btn-gradient:hover {
  background: linear-gradient(135deg, #2563eb, #1e40af);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.2);
}

.btn-success {
  background: linear-gradient(135deg, #10b981, #047857);
}

.btn-success:hover {
  background: linear-gradient(135deg, #059669, #065f46);
  box-shadow: 0 10px 20px rgba(16, 185, 129, 0.3);
}

.btn-danger {
  background: linear-gradient(135deg, #ef4444, #dc2626);
}

.btn-danger:hover {
  background: linear-gradient(135deg, #dc2626, #b91c1c);
  box-shadow: 0 10px 20px rgba(239, 68, 68, 0.3);
}

/* Navigation styles */
.nav-tab {
  position: relative;
  transition: all 0.3s ease;
}

.nav-tab::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 50%;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
  transition: all 0.3s ease;
  transform: translateX(-50%);
}

.nav-tab.active::after {
  width: 100%;
}

.nav-tab:hover::after {
  width: 100%;
}

/* Custom scrollbar for dark theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: #475569;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

/* Form input dark theme */
.input-dark {
  background: rgba(30, 41, 59, 0.5);
  border: 1px solid rgba(148, 163, 184, 0.2);
  color: #e2e8f0;
}

.input-dark:focus {
  background: rgba(30, 41, 59, 0.8);
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.input-dark::placeholder {
  color: #94a3b8;
}
