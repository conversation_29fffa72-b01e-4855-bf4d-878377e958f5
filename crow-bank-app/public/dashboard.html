<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SecureBank - Dashboard</title>
    <link rel="stylesheet" href="style.css" />
  </head>
  <body
    class="bg-slate-900 min-h-screen text-slate-100 relative overflow-x-hidden"
    style="background-color: #0b1222"
  >
    <!-- Static Background Pattern -->
    <div
      class="fixed inset-0 bg-static-dots opacity-30 pointer-events-none"
    ></div>

    <!-- Navigation -->
    <nav
      class="bg-slate-800/50 backdrop-blur-sm sticky top-0 z-40 border-b border-slate-700/30"
    >
      <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center py-4">
          <div class="flex items-center space-x-3">
            <div
              class="w-8 h-8 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-lg flex items-center justify-center"
            >
              <span class="text-white font-bold text-sm">🏦</span>
            </div>
            <h1
              class="text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent"
            >
              SecureBank
            </h1>
          </div>
          <div class="flex items-center space-x-4">
            <div class="hidden sm:block">
              <span id="userName" class="text-slate-300 font-medium"
                >Welcome, User</span
              >
            </div>
            <button
              id="logoutBtn"
              class="bg-slate-700/50 hover:bg-red-500/20 p-3 rounded-lg text-slate-300 hover:text-red-400 transition-all duration-200 group"
              title="Logout"
            >
              <svg
                class="w-5 h-5 transition-transform duration-300 group-hover:scale-110"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                ></path>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </nav>

    <div
      class="relative z-10 w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4 sm:py-6"
    >
      <!-- Compact Account Overview Cards -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <!-- Balance Card -->
        <div
          class="glass-card rounded-xl p-4 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
        >
          <div class="flex items-center justify-between mb-3">
            <div
              class="w-12 h-12 bg-gradient-to-br from-emerald-500 to-green-600 rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                ></path>
              </svg>
            </div>
            <button
              id="refreshBtn"
              class="text-slate-400 hover:text-blue-400 transition-colors p-2 rounded-lg hover:bg-slate-700/50"
            >
              <svg
                class="w-5 h-5"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
                ></path>
              </svg>
            </button>
          </div>
          <div>
            <p class="text-slate-400 text-sm font-medium mb-1">
              Current Balance
            </p>
            <p id="balance" class="text-2xl font-bold text-emerald-400">
              $0.00
            </p>
          </div>
        </div>

        <!-- Account Info Card -->
        <div
          class="glass-card rounded-xl p-4 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
        >
          <div class="flex items-center mb-3">
            <div
              class="w-12 h-12 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"
                ></path>
              </svg>
            </div>
          </div>
          <div>
            <p class="text-slate-400 text-sm font-medium mb-1">
              Account Number
            </p>
            <p
              id="accountNumber"
              class="text-lg font-semibold text-slate-200 font-mono"
            >
              Loading...
            </p>
          </div>
        </div>

        <!-- Account Holder Card -->
        <div
          class="glass-card rounded-xl p-4 transition-all duration-300 hover:-translate-y-1 hover:shadow-xl"
        >
          <div class="flex items-center mb-3">
            <div
              class="w-12 h-12 bg-gradient-to-br from-purple-500 to-pink-600 rounded-xl flex items-center justify-center"
            >
              <svg
                class="w-6 h-6 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                ></path>
              </svg>
            </div>
          </div>
          <div>
            <p class="text-slate-400 text-sm font-medium mb-1">
              Account Holder
            </p>
            <p id="accountHolder" class="text-lg font-semibold text-slate-200">
              Loading...
            </p>
          </div>
        </div>
      </div>

      <!-- Navigation Tabs -->
      <div class="bg-slate-800/50 rounded-lg p-1 mb-3 sm:mb-4">
        <nav class="grid grid-cols-5 gap-1">
          <button
            data-section="balance"
            class="nav-tab active px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium rounded-md transition-all duration-300 flex items-center justify-center space-x-1"
          >
            <svg
              class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
              ></path>
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
              ></path>
            </svg>
            <span class="hidden sm:inline">Overview</span>
          </button>
          <button
            data-section="deposit"
            class="nav-tab px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium rounded-md transition-all duration-300 flex items-center justify-center space-x-1"
          >
            <svg
              class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 6v6m0 0v6m0-6h6m-6 0H6"
              ></path>
            </svg>
            <span class="hidden sm:inline">Deposit</span>
          </button>
          <button
            data-section="withdraw"
            class="nav-tab px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium rounded-md transition-all duration-300 flex items-center justify-center space-x-1"
          >
            <svg
              class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M20 12H4"
              ></path>
            </svg>
            <span class="hidden sm:inline">Withdraw</span>
          </button>
          <button
            data-section="transfer"
            class="nav-tab px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium rounded-md transition-all duration-300 flex items-center justify-center space-x-1"
          >
            <svg
              class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
              ></path>
            </svg>
            <span class="hidden sm:inline">Transfer</span>
          </button>
          <button
            data-section="history"
            class="nav-tab px-2 sm:px-3 py-2 text-xs sm:text-sm font-medium rounded-md transition-all duration-300 flex items-center justify-center space-x-1"
          >
            <svg
              class="w-3 h-3 sm:w-4 sm:h-4 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
              ></path>
            </svg>
            <span class="hidden sm:inline">History</span>
          </button>
        </nav>
      </div>

      <!-- Dashboard Sections -->

      <!-- Balance/Overview Section -->
      <div id="balanceSection" class="dashboard-section active">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Account Details -->
          <div class="lg:col-span-2">
            <div class="glass-card rounded-xl p-6">
              <h3 class="text-xl font-semibold text-slate-100 mb-6">
                Account Details
              </h3>
              <div class="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div class="space-y-4">
                  <div class="border-l-4 border-blue-500 pl-4">
                    <p class="text-slate-400 text-sm">Account Holder</p>
                    <p
                      id="accountHolderDetail"
                      class="text-slate-200 font-medium"
                    >
                      Loading...
                    </p>
                  </div>
                  <div class="border-l-4 border-emerald-500 pl-4">
                    <p class="text-slate-400 text-sm">Account Type</p>
                    <p class="text-slate-200 font-medium">Savings Account</p>
                  </div>
                </div>
                <div class="space-y-4">
                  <div class="border-l-4 border-purple-500 pl-4">
                    <p class="text-slate-400 text-sm">Account Number</p>
                    <p
                      id="accountNumberDetail"
                      class="text-slate-200 font-medium font-mono"
                    >
                      Loading...
                    </p>
                  </div>
                  <div class="border-l-4 border-amber-500 pl-4">
                    <p class="text-slate-400 text-sm">Current Balance</p>
                    <p
                      id="balanceDetail"
                      class="text-emerald-400 font-bold text-xl"
                    >
                      $0.00
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Quick Stats -->
          <div class="glass-card rounded-xl p-6">
            <h3 class="text-xl font-semibold text-slate-100 mb-6">
              Quick Stats
            </h3>
            <div class="space-y-4">
              <div
                class="flex justify-between items-center py-3 border-b border-slate-700"
              >
                <span class="text-slate-400">Total Deposits</span>
                <span class="text-emerald-400 font-semibold">$0.00</span>
              </div>
              <div
                class="flex justify-between items-center py-3 border-b border-slate-700"
              >
                <span class="text-slate-400">Total Withdrawals</span>
                <span class="text-red-400 font-semibold">$0.00</span>
              </div>
              <div class="flex justify-between items-center py-3">
                <span class="text-slate-400">Net Balance</span>
                <span class="text-blue-400 font-semibold">$0.00</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Deposit Section -->
      <div id="depositSection" class="dashboard-section">
        <div class="glass-card rounded-xl p-6 max-w-md mx-auto">
          <h3
            class="text-xl font-semibold text-slate-100 mb-6 flex items-center"
          >
            <span
              class="w-8 h-8 bg-gradient-to-r from-emerald-500 to-green-600 rounded-lg flex items-center justify-center mr-3"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                ></path>
              </svg>
            </span>
            Deposit Money
          </h3>
          <form id="depositForm" class="space-y-4">
            <div>
              <label
                for="depositAmount"
                class="block text-sm font-medium text-slate-300 mb-2"
              >
                Amount to Deposit
              </label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <span class="text-slate-400">$</span>
                </div>
                <input
                  type="number"
                  id="depositAmount"
                  name="amount"
                  step="0.01"
                  min="0.01"
                  required
                  class="input-dark block w-full pl-8 pr-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-emerald-500 focus:outline-none"
                  placeholder="0.00"
                />
              </div>
            </div>
            <button
              type="submit"
              class="btn-success w-full py-3 px-6 rounded-lg font-medium text-white transition-all duration-300"
            >
              Deposit Funds
            </button>
          </form>
        </div>
      </div>

      <!-- Withdraw Section -->
      <div id="withdrawSection" class="dashboard-section">
        <div class="glass-card rounded-xl p-6 max-w-md mx-auto">
          <h3
            class="text-xl font-semibold text-slate-100 mb-6 flex items-center"
          >
            <span
              class="w-8 h-8 bg-gradient-to-r from-red-500 to-red-600 rounded-lg flex items-center justify-center mr-3"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M20 12H4"
                ></path>
              </svg>
            </span>
            Withdraw Money
          </h3>
          <form id="withdrawForm" class="space-y-4">
            <div>
              <label
                for="withdrawAmount"
                class="block text-sm font-medium text-slate-300 mb-2"
              >
                Amount to Withdraw
              </label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <span class="text-slate-400">$</span>
                </div>
                <input
                  type="number"
                  id="withdrawAmount"
                  name="amount"
                  step="0.01"
                  min="0.01"
                  required
                  class="input-dark block w-full pl-8 pr-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-red-500 focus:outline-none"
                  placeholder="0.00"
                />
              </div>
            </div>
            <button
              type="submit"
              class="btn-danger w-full py-3 px-6 rounded-lg font-medium text-white transition-all duration-300"
            >
              Withdraw Funds
            </button>
          </form>
        </div>
      </div>

      <!-- Transfer Section -->
      <div id="transferSection" class="dashboard-section">
        <div class="glass-card rounded-xl p-6 max-w-md mx-auto">
          <h3
            class="text-xl font-semibold text-slate-100 mb-6 flex items-center"
          >
            <span
              class="w-8 h-8 bg-gradient-to-r from-blue-500 to-indigo-600 rounded-lg flex items-center justify-center mr-3"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                ></path>
              </svg>
            </span>
            Transfer Money
          </h3>
          <form id="transferForm" class="space-y-4">
            <div>
              <label
                for="transferEmail"
                class="block text-sm font-medium text-slate-300 mb-2"
              >
                Recipient Email Address
              </label>
              <input
                type="email"
                id="transferEmail"
                name="email"
                required
                class="input-dark block w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                placeholder="<EMAIL>"
              />
            </div>
            <div>
              <label
                for="transferAmount"
                class="block text-sm font-medium text-slate-300 mb-2"
              >
                Amount to Transfer
              </label>
              <div class="relative">
                <div
                  class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
                >
                  <span class="text-slate-400">$</span>
                </div>
                <input
                  type="number"
                  id="transferAmount"
                  name="amount"
                  step="0.01"
                  min="0.01"
                  required
                  class="input-dark block w-full pl-8 pr-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none"
                  placeholder="0.00"
                />
              </div>
            </div>
            <button
              type="submit"
              class="btn-gradient w-full py-3 px-6 rounded-lg font-medium text-white transition-all duration-300"
            >
              Transfer Funds
            </button>
          </form>
        </div>
      </div>

      <!-- History Section -->
      <div id="historySection" class="dashboard-section">
        <div class="glass-card rounded-xl p-6">
          <h3
            class="text-xl font-semibold text-slate-100 mb-6 flex items-center"
          >
            <span
              class="w-8 h-8 bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg flex items-center justify-center mr-3"
            >
              <svg
                class="w-5 h-5 text-white"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  stroke-width="2"
                  d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                ></path>
              </svg>
            </span>
            Transaction History
          </h3>
          <div id="transactionHistory" class="space-y-4">
            <div class="flex items-center justify-center py-12">
              <div class="text-center">
                <div
                  class="animate-spin rounded-full h-8 w-8 border-b-2 border-purple-400 mx-auto mb-4"
                ></div>
                <p class="text-slate-400">Loading transaction history...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="/js/utils.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/auth.js"></script>
    <script src="/js/dashboard.js"></script>

    <style>
      .dashboard-section {
        display: none;
      }

      .dashboard-section.active {
        display: block;
        animation: slideIn 0.3s ease-out;
      }

      .nav-tab {
        color: #94a3b8;
        background: transparent;
        border: 1px solid transparent;
      }

      /* Make child elements non-interactive so clicks pass through to button */
      .nav-tab svg,
      .nav-tab span {
        pointer-events: none;
      }

      .nav-tab:hover {
        color: #e2e8f0;
        background: rgba(59, 130, 246, 0.1);
        border-color: rgba(59, 130, 246, 0.2);
        transform: translateY(-1px);
      }

      .nav-tab.active {
        color: #ffffff;
        background: linear-gradient(
          135deg,
          rgba(59, 130, 246, 0.3),
          rgba(29, 78, 216, 0.2)
        );
        border-color: rgba(59, 130, 246, 0.4);
        box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
      }

      .nav-tab.active:hover {
        transform: translateY(-1px);
        box-shadow: 0 6px 16px rgba(59, 130, 246, 0.35);
      }

      @keyframes slideIn {
        from {
          opacity: 0;
          transform: translateY(20px);
        }
        to {
          opacity: 1;
          transform: translateY(0);
        }
      }

      /* Hide scrollbar but keep functionality */
      .scrollbar-hide {
        -ms-overflow-style: none;
        scrollbar-width: none;
      }

      .scrollbar-hide::-webkit-scrollbar {
        display: none;
      }
    </style>
  </body>
</html>
