<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SecureBank - Login</title>
    <link rel="stylesheet" href="style.css" />
    <style>
      /*For that auto fill colour by browser :( */
      input:-webkit-autofill {
        box-shadow: 0 0 0px 1000px #172234 inset !important;
        -webkit-text-fill-color: #90a1b9 !important;
        border: 1px solid #334155 !important;
        caret-color: #cbd5e1 !important;
        /* border-radius: 0.5rem; */
        transition: background-color 9999s ease-in-out 0s;
      }
    </style>
  </head>
  <body
    class="min-h-screen bg-[#0b1222] relative overflow-x-hidden overflow-y-auto flex items-center justify-center"
  >
    <!-- Static Background Pattern -->
    <div class="fixed inset-0 bg-static-dots pointer-events-none"></div>

    <div class="relative z-10 max-w-md w-full mx-4">
      <!-- Logo -->
      <div class="text-center mb-8">
        <div class="flex justify-center mb-4">
          <div
            class="w-16 h-16 bg-gradient-to-br from-blue-500 to-cyan-500 flex items-center justify-center bg-slate-800/50 rounded-full"
          >
            <span class="text-white font-bold text-2xl select-none">🏦</span>
          </div>
        </div>
        <h1
          class="text-3xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent"
        >
          Welcome Back!
        </h1>
        <p class="mt-2 text-slate-400">Please enter your details to sign in.</p>
      </div>

      <!-- Login Form Card-->
      <div
        class="bg-slate-800/30 backdrop-blur-sm rounded-2xl p-8 border border-slate-700/30"
      >
        <form id="loginForm" class="space-y-6">
          <div>
            <label
              for="loginEmail"
              class="block text-sm font-medium text-slate-300 mb-2"
              >Email Address</label
            >
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-slate-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207"
                  ></path>
                </svg>
              </div>
              <input
                type="email"
                id="loginEmail"
                name="email"
                required
                class="input-dark block w-full pl-10 pr-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
                placeholder="Enter your email address"
              />
            </div>
          </div>

          <div>
            <label
              for="loginPassword"
              class="block text-sm font-medium text-slate-300 mb-2"
              >Password</label
            >
            <div class="relative">
              <div
                class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"
              >
                <svg
                  class="w-5 h-5 text-slate-400"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"
                  ></path>
                </svg>
              </div>
              <input
                type="password"
                id="loginPassword"
                name="password"
                required
                class="input-dark block w-full pl-10 pr-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
                placeholder="Enter your password"
              />
            </div>
          </div>

          <div class="flex items-center justify-between">
            <div class="flex items-center">
              <input
                id="remember"
                name="remember"
                type="checkbox"
                class="h-4 w-4 rounded border-slate-600 bg-slate-700 text-blue-500 focus:ring-blue-500 focus:ring-offset-slate-800"
              />
              <label for="remember" class="ml-2 block text-sm text-slate-300"
                >Remember me</label
              >
            </div>
            <a
              href="#"
              class="text-sm text-blue-400 hover:text-blue-300 transition-colors"
              >Forgot password?</a
            >
          </div>

          <button
            type="submit"
            class="btn-gradient w-full py-3 px-6 rounded-lg font-medium text-white transition-all duration-300 flex items-center justify-center space-x-2 cursor-pointer"
          >
            <svg
              class="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
              ></path>
            </svg>
            <span>Log In</span>
          </button>
        </form>

        <!-- Social Login Section -->
        <div class="mt-6">
          <div class="relative">
            <div class="absolute inset-0 flex items-center">
              <div class="w-full border-t border-slate-600"></div>
            </div>
            <div class="relative flex justify-center text-sm">
              <span class="px-2 bg-slate-800 text-slate-400"
                >Or continue with</span
              >
            </div>
          </div>

          <div class="mt-6 grid grid-cols-3 gap-3">
            <!-- Google -->
            <button
              type="button"
              class="w-full inline-flex justify-center py-3 px-4 rounded-lg border border-slate-600 bg-slate-700/50 hover:bg-slate-600/50 transition-all duration-200 group"
            >
              <svg
                class="w-5 h-5 text-white group-hover:scale-110 transition-transform"
                viewBox="0 0 24 24"
              >
                <path
                  fill="currentColor"
                  d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                />
                <path
                  fill="currentColor"
                  d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                />
                <path
                  fill="currentColor"
                  d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                />
                <path
                  fill="currentColor"
                  d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                />
              </svg>
            </button>

            <!-- Apple -->
            <button
              type="button"
              class="w-full inline-flex justify-center py-3 px-4 rounded-lg border border-slate-600 bg-slate-700/50 hover:bg-slate-600/50 transition-all duration-200 group"
            >
              <svg
                class="w-5 h-5 text-white group-hover:scale-110 transition-transform"
                viewBox="0 0 24 24"
              >
                <path
                  fill="currentColor"
                  d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"
                />
              </svg>
            </button>

            <!-- Twitter -->
            <button
              type="button"
              class="w-full inline-flex justify-center py-3 px-4 rounded-lg border border-slate-600 bg-slate-700/50 hover:bg-slate-600/50 transition-all duration-200 group"
            >
              <svg
                class="w-5 h-5 text-white group-hover:scale-110 transition-transform"
                viewBox="0 0 24 24"
              >
                <path
                  fill="currentColor"
                  d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"
                />
              </svg>
            </button>
          </div>
        </div>

        <!-- Error Message -->
        <div
          id="errorMessage"
          class="mt-6 p-4 bg-red-500/10 border border-red-500/20 text-red-400 rounded-lg hidden backdrop-blur-sm"
        >
          <div class="flex items-center space-x-2">
            <svg
              class="w-5 h-5 flex-shrink-0"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                stroke-linecap="round"
                stroke-linejoin="round"
                stroke-width="2"
                d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
              ></path>
            </svg>
            <p class="text-sm"></p>
          </div>
        </div>

        <div class="mt-6 text-center">
          <p class="text-sm text-slate-400 mt-7">
            Don't have an account?
            <a
              href="/register"
              class="text-blue-400 hover:text-blue-300 font-medium transition-colors"
              >Sign up here</a
            >
          </p>
        </div>
      </div>
    </div>

    <!-- JavaScript Modules -->
    <script src="/js/utils.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/auth.js"></script>
    <script>
      document.addEventListener("DOMContentLoaded", function () {
        const loginForm = document.getElementById("loginForm");

        // Check if user just registered
        const urlParams = new URLSearchParams(window.location.search);
        if (urlParams.get("registered") === "true") {
          showSuccessMessage("Account created successfully!");
          // Clean the URL
          window.history.replaceState(
            {},
            document.title,
            window.location.pathname
          );
        }

        loginForm.addEventListener("submit", async function (e) {
          e.preventDefault();

          const email = document.getElementById("loginEmail").value.trim();
          const password = document.getElementById("loginPassword").value;

          // Basic validation
          if (!email || !password) {
            showError("Please fill in all fields");
            return;
          }

          if (!window.utils.validateEmail(email)) {
            showError("Please enter a valid email address");
            return;
          }

          // Disable submit button during login
          const submitBtn = loginForm.querySelector('button[type="submit"]');
          const originalText = submitBtn.textContent;
          submitBtn.disabled = true;
          submitBtn.textContent = "Signing In...";

          try {
            await window.auth.login(email, password);
          } catch (error) {
            showError(error.message || "Login failed");
          } finally {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
          }
        });

        function showError(message) {
          const errorDiv = document.getElementById("errorMessage");
          errorDiv.querySelector("p").textContent = message;
          errorDiv.classList.remove("hidden");

          // Hide error after 5 seconds
          setTimeout(() => {
            errorDiv.classList.add("hidden");
          }, 5000);
        }

        function showSuccessMessage(message) {
          const successDiv = document.createElement("div");
          successDiv.className =
            "success-message-overlay fixed top-4 inset-x-0 mx-auto w-fit bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg backdrop-blur-sm animate-slideDown";
          successDiv.style.zIndex = "99999";
          successDiv.innerHTML = `
            <div class="flex items-center space-x-2">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
              </svg>
              <span class="font-medium">${message}</span>
            </div>
          `;
          document.body.appendChild(successDiv);

          // Remove success message after 4 seconds
          setTimeout(() => {
            successDiv.style.opacity = "0";
            setTimeout(() => successDiv.remove(), 300);
          }, 4000);
        }
      });
    </script>
  </body>
</html>
