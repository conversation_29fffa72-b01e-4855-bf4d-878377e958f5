<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>SecureBank - Register Account</title>
    <link rel="stylesheet" href="style.css" />
    <style>
      /*For that auto fill colour by browser :( */
      input:-webkit-autofill {
        box-shadow: 0 0 0px 1000px #172234 inset !important;
        -webkit-text-fill-color: #90a1b9 !important;
        border: 1px solid #334155 !important;
        caret-color: #cbd5e1 !important;
        /* border-radius: 0.5rem; */
        transition: background-color 9999s ease-in-out 0s;
      }
    </style>
    <!-- Script for svg loading -->
    <script type="text/javascript">
      document.addEventListener("DOMContentLoaded", function () {
        window.setTimeout(
          document.querySelector("svg").classList.add("animated"),
          1000
        );
      });
    </script>
  </head>
  <body
    class="min-h-screen bg-[#0b1222] text-slate-100 relative overflow-x-hidden overflow-y-auto 2xl:overflow-hidden"
  >
    <!-- Static Background Pattern -->
    <div class="fixed inset-0 bg-static-dots pointer-events-none"></div>

    <!-- Header -->
    <header
      class="bg-slate-800/10 backdrop-blur-2xl sticky top-0 z-40 border-b-1 border-slate-700/30"
    >
      <div class="px-4 sm:px-6 lg:px-8">
        <div class="flex justify-start items-center py-4">
          <div class="flex items-center space-x-3">
            <div
              class="size-10 bg-gradient-to-br from-blue-500 to-cyan-500 rounded-full flex items-center justify-center"
            >
              <span class="text-white font-bold text-lg select-none">🏦</span>
            </div>
            <a
              class="text-xl font-bold bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent cursor-pointer"
              href="./index.html"
            >
              SecureBank
            </a>
          </div>
        </div>
      </div>
    </header>

    <!-- Hero Section -->

    <main class="relative z-10 2xl:ml-48 px-4 sm:px-6 lg:px-8 py-4 sm:py-6">
      <div class="lg:grid lg:grid-cols-2 lg:gap-16 items-center">
        <!-- Left Column - Hero Content -->
        <div class="mb-12 lg:mb-0 lg:pr-16">
          <div class="flex items-center flex-col justify-center">
            <h1
              class="text-4xl font-bold pl-4 text-slate-100 sm:text-5xl md:text-6xl leading-tight"
            >
              Your Money,
              <span
                class="bg-gradient-to-r from-blue-400 to-cyan-400 bg-clip-text text-transparent"
                >Secured</span
              >
            </h1>

            <!-- Main Svg -->
            <div class="flex items-center justify-center">
              <svg
                class="animated size-56 md:size-86 lg:size-114"
                id="bank-illustration"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 500 500"
                version="1.1"
                xmlns:xlink="http://www.w3.org/1999/xlink"
                xmlns:svgjs="http://svgjs.com/svgjs"
              >
                <g
                  id="freepik--background-simple--inject-4"
                  class="animable"
                  style="transform-origin: 251.326px 245.544px"
                >
                  <path
                    d="M332.51,63.83c-21.82,12.48-37.21,33.47-55.86,50.32a179.7,179.7,0,0,1-81.92,42c-25,5.41-51.12,5.49-75.21,14-47,16.49-79.19,65.43-80,115.19s27.5,98,69.11,125.28a165.64,165.64,0,0,0,46,20.95c30.6,8.61,63.05,8.23,94.67,4.94,54.62-5.69,111.88-36.71,146.31-80.44a313.74,313.74,0,0,0,38.64-63.45,316.45,316.45,0,0,0,28.52-118.15c1.45-35,0-73.8-27.77-99.31S365.06,45.2,332.51,63.83Z"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 251.326px 245.544px;
                    "
                    id="elvgp1wr7vln"
                    class="animable"
                  ></path>
                  <g id="elka6eeekp7f">
                    <path
                      d="M332.51,63.83c-21.82,12.48-37.21,33.47-55.86,50.32a179.7,179.7,0,0,1-81.92,42c-25,5.41-51.12,5.49-75.21,14-47,16.49-79.19,65.43-80,115.19s27.5,98,69.11,125.28a165.64,165.64,0,0,0,46,20.95c30.6,8.61,63.05,8.23,94.67,4.94,54.62-5.69,111.88-36.71,146.31-80.44a313.74,313.74,0,0,0,38.64-63.45,316.45,316.45,0,0,0,28.52-118.15c1.45-35,0-73.8-27.77-99.31S365.06,45.2,332.51,63.83Z"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.7;
                        transform-origin: 251.326px 245.544px;
                      "
                      class="animable"
                    ></path>
                  </g>
                </g>
                <g
                  id="freepik--Shadow--inject-4"
                  class="animable"
                  style="transform-origin: 275.1px 416.25px"
                >
                  <ellipse
                    cx="275.1"
                    cy="416.25"
                    rx="179.33"
                    ry="48.3"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 275.1px 416.25px;
                    "
                    id="ely166m6wkz78"
                    class="animable"
                  ></ellipse>
                  <g id="eldz3c6p1oh3a">
                    <ellipse
                      cx="275.1"
                      cy="416.25"
                      rx="179.33"
                      ry="48.3"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.5;
                        transform-origin: 275.1px 416.25px;
                      "
                      class="animable"
                    ></ellipse>
                  </g>
                </g>
                <g
                  id="freepik--Window--inject-4"
                  class="animable"
                  style="transform-origin: 191.545px 123.91px"
                >
                  <rect
                    x="77.18"
                    y="68.3"
                    width="228.73"
                    height="136.09"
                    rx="8.95"
                    style="
                      fill: rgb(255, 255, 255);
                      transform-origin: 191.545px 136.345px;
                    "
                    id="elj30x9gjpifl"
                    class="animable"
                  ></rect>
                  <path
                    d="M305.91,77.25v3.54H77.18V77.25a9,9,0,0,1,8.95-9H297A8.94,8.94,0,0,1,305.91,77.25Z"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 191.545px 74.52px;
                    "
                    id="elsalqramwaq"
                    class="animable"
                  ></path>
                  <rect
                    x="77.18"
                    y="68.3"
                    width="228.73"
                    height="136.09"
                    rx="8.95"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 191.545px 136.345px;
                    "
                    id="el2qzrrvsbvzk"
                    class="animable"
                  ></rect>
                  <path
                    d="M305.91,77.25v3.54H77.18V77.25a9,9,0,0,1,8.95-9H297A8.94,8.94,0,0,1,305.91,77.25Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 191.545px 74.52px;
                    "
                    id="ellfoqg62ml1h"
                    class="animable"
                  ></path>
                  <path
                    d="M102.87,74.54a2.29,2.29,0,1,1-2.29-2.28A2.29,2.29,0,0,1,102.87,74.54Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 100.58px 74.55px;
                    "
                    id="elovhqtab53u"
                    class="animable"
                  ></path>
                  <path
                    d="M114.82,74.54a2.29,2.29,0,1,1-2.29-2.28A2.29,2.29,0,0,1,114.82,74.54Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 112.53px 74.55px;
                    "
                    id="el8c8uoynj677"
                    class="animable"
                  ></path>
                  <circle
                    cx="124.48"
                    cy="74.54"
                    r="2.29"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 124.48px 74.54px;
                    "
                    id="el1qm312yvc87"
                    class="animable"
                  ></circle>
                  <rect
                    x="90.74"
                    y="90.7"
                    width="72.05"
                    height="38.49"
                    rx="3.69"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 126.765px 109.945px;
                    "
                    id="elnx4fun26cdj"
                    class="animable"
                  ></rect>
                  <line
                    x1="94.98"
                    y1="124.35"
                    x2="157.45"
                    y2="124.35"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 126.215px 124.35px;
                    "
                    id="el4h9924dj9xg"
                    class="animable"
                  ></line>
                  <rect
                    x="99.62"
                    y="117"
                    width="7.25"
                    height="7.25"
                    style="
                      fill: rgb(47, 107, 224);
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 103.245px 120.625px;
                    "
                    id="el7iz0zlwcpbh"
                    class="animable"
                  ></rect>
                  <rect
                    x="110.23"
                    y="107.47"
                    width="7.25"
                    height="16.88"
                    style="
                      fill: rgb(47, 107, 224);
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 113.855px 115.91px;
                    "
                    id="elyqvmtfwf0m"
                    class="animable"
                  ></rect>
                  <rect
                    x="122.18"
                    y="110.37"
                    width="7.25"
                    height="14.15"
                    style="
                      fill: rgb(47, 107, 224);
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 125.805px 117.445px;
                    "
                    id="elju0hyfn9jn"
                    class="animable"
                  ></rect>
                  <rect
                    x="134.74"
                    y="96.28"
                    width="7.25"
                    height="27.75"
                    style="
                      fill: rgb(47, 107, 224);
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 138.365px 110.155px;
                    "
                    id="elbpvkaurkxzi"
                    class="animable"
                  ></rect>
                  <rect
                    x="147.29"
                    y="96.5"
                    width="7.25"
                    height="27.75"
                    style="
                      fill: rgb(47, 107, 224);
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 150.915px 110.375px;
                    "
                    id="elq5ay0fbpt4b"
                    class="animable"
                  ></rect>
                  <circle
                    cx="252.12"
                    cy="110.91"
                    r="20.27"
                    style="
                      fill: rgb(47, 107, 224);
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 252.12px 110.91px;
                    "
                    id="el8fuei7p3oba"
                    class="animable"
                  ></circle>
                  <path
                    d="M253.78,123.55v2.08h-3.4v-2.08a6.18,6.18,0,0,1-4.72-6.3v-.94l3.85-.42v1c0,1.93,1,3.1,2.57,3.1a2.37,2.37,0,0,0,2.56-2.53c0-1.85-1.73-3.44-3.77-5.13-2.42-2-4.83-4.38-4.83-7.74s1.58-5.63,4.34-6.31V96.19h3.4v2.07c2.79.65,4.41,3,4.41,6.35v.68l-3.85.41V105c0-2-.79-3.1-2.19-3.1s-2.19.87-2.19,2.53c0,1.85,1.78,3.4,3.78,5.13,2.38,2,4.83,4.38,4.83,7.74A6.15,6.15,0,0,1,253.78,123.55Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 252.114px 110.91px;
                    "
                    id="elln9pwi8fcie"
                    class="animable"
                  ></path>
                  <line
                    x1="89.06"
                    y1="148.85"
                    x2="94.98"
                    y2="148.85"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 92.02px 148.85px;
                    "
                    id="elzkft4gg2xjs"
                    class="animable"
                  ></line>
                  <line
                    x1="89.06"
                    y1="160.29"
                    x2="94.98"
                    y2="160.29"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 92.02px 160.29px;
                    "
                    id="el65twpuh6m6e"
                    class="animable"
                  ></line>
                  <line
                    x1="89.06"
                    y1="171.74"
                    x2="94.98"
                    y2="171.74"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 92.02px 171.74px;
                    "
                    id="elj9pzb0y51cb"
                    class="animable"
                  ></line>
                  <line
                    x1="89.06"
                    y1="183.18"
                    x2="94.98"
                    y2="183.18"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 92.02px 183.18px;
                    "
                    id="elwbd36di4la"
                    class="animable"
                  ></line>
                  <line
                    x1="108.03"
                    y1="191.82"
                    x2="108.03"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 108.03px 189.52px;
                    "
                    id="elo4qnf1vcqf"
                    class="animable"
                  ></line>
                  <line
                    x1="117.41"
                    y1="191.82"
                    x2="117.41"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 117.41px 189.52px;
                    "
                    id="elkg5ru9u1o1o"
                    class="animable"
                  ></line>
                  <line
                    x1="126.79"
                    y1="191.82"
                    x2="126.79"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 126.79px 189.52px;
                    "
                    id="elffie9vnfc2f"
                    class="animable"
                  ></line>
                  <line
                    x1="136.18"
                    y1="191.82"
                    x2="136.18"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 136.18px 189.52px;
                    "
                    id="elhzjy1tnk7yr"
                    class="animable"
                  ></line>
                  <line
                    x1="145.56"
                    y1="191.82"
                    x2="145.56"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 145.56px 189.52px;
                    "
                    id="elh9kous9np5u"
                    class="animable"
                  ></line>
                  <line
                    x1="154.94"
                    y1="191.82"
                    x2="154.94"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 154.94px 189.52px;
                    "
                    id="elrier9jztz3"
                    class="animable"
                  ></line>
                  <line
                    x1="164.32"
                    y1="191.82"
                    x2="164.32"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 164.32px 189.52px;
                    "
                    id="elx16s4vthe3"
                    class="animable"
                  ></line>
                  <line
                    x1="173.71"
                    y1="191.82"
                    x2="173.71"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 173.71px 189.52px;
                    "
                    id="elclkmw067kyb"
                    class="animable"
                  ></line>
                  <line
                    x1="183.09"
                    y1="191.82"
                    x2="183.09"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 183.09px 189.52px;
                    "
                    id="elb3c9xcdagg9"
                    class="animable"
                  ></line>
                  <line
                    x1="192.47"
                    y1="191.82"
                    x2="192.47"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 192.47px 189.52px;
                    "
                    id="el15fcz8u5nfr"
                    class="animable"
                  ></line>
                  <line
                    x1="201.85"
                    y1="191.82"
                    x2="201.85"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 201.85px 189.52px;
                    "
                    id="ellsu5e4pede"
                    class="animable"
                  ></line>
                  <line
                    x1="211.24"
                    y1="191.82"
                    x2="211.24"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 211.24px 189.52px;
                    "
                    id="elye3bu3bbkp"
                    class="animable"
                  ></line>
                  <line
                    x1="220.62"
                    y1="191.82"
                    x2="220.62"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 220.62px 189.52px;
                    "
                    id="elny12lvwz0lh"
                    class="animable"
                  ></line>
                  <line
                    x1="230"
                    y1="191.82"
                    x2="230"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 230px 189.52px;
                    "
                    id="elho6bqrxd1yq"
                    class="animable"
                  ></line>
                  <line
                    x1="239.38"
                    y1="191.82"
                    x2="239.38"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 239.38px 189.52px;
                    "
                    id="elu1j7pkmrm7s"
                    class="animable"
                  ></line>
                  <line
                    x1="248.41"
                    y1="191.82"
                    x2="248.41"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 248.41px 189.52px;
                    "
                    id="eljob5l1qh9p"
                    class="animable"
                  ></line>
                  <line
                    x1="257.44"
                    y1="191.82"
                    x2="257.44"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 257.44px 189.52px;
                    "
                    id="elkid61vwk7cs"
                    class="animable"
                  ></line>
                  <line
                    x1="266.46"
                    y1="191.82"
                    x2="266.46"
                    y2="187.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 266.46px 189.52px;
                    "
                    id="el7q608m7a90s"
                    class="animable"
                  ></line>
                  <path
                    d="M290.34,141.85v44.46c0,3.05-3.1,5.51-6.92,5.51H96a7.42,7.42,0,0,1-6-2.71,4.73,4.73,0,0,1-1-2.8V141.85c0-3,3.1-5.51,6.92-5.51H283.42C287.24,136.34,290.34,138.8,290.34,141.85Z"
                    style="
                      fill: none;
                      stroke: rgb(47, 107, 224);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 189.67px 164.082px;
                    "
                    id="ele73ng01z1gp"
                    class="animable"
                  ></path>
                  <path
                    d="M233.3,55.74l-59.83,80.6-22.88,30.82-25-15.92L95,191.82h-.44a5.51,5.51,0,0,1-4.75-2.71l34.37-45.61,25,15.89,17.11-23,62.39-84Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 161.555px 122.105px;
                    "
                    id="el5nb0o9569ck"
                    class="animable"
                  ></path>
                  <polygon
                    points="218.12 56.76 240.11 43.43 234.03 66.49 230.15 55.82 218.12 56.76"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 229.115px 54.96px;
                    "
                    id="elz93aeqm1g1"
                    class="animable"
                  ></polygon>
                </g>
                <g
                  id="freepik--Floor--inject-4"
                  class="animable"
                  style="transform-origin: 230.675px 235.675px"
                >
                  <polyline
                    points="426.23 310.52 74.54 310.52 35.12 345.57"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 230.675px 328.045px;
                    "
                    id="elomnhccgzrlb"
                    class="animable"
                  ></polyline>
                  <line
                    x1="74.76"
                    y1="285.98"
                    x2="74.76"
                    y2="217.83"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 74.76px 251.905px;
                    "
                    id="eloe93k7nlpp"
                    class="animable"
                  ></line>
                  <line
                    x1="74.76"
                    y1="306.56"
                    x2="74.76"
                    y2="293.38"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 74.76px 299.97px;
                    "
                    id="elunsrf8jxrzq"
                    class="animable"
                  ></line>
                  <g
                    id="freepik--freepik--Plants--inject-120--inject-4"
                    class="animable"
                    style="transform-origin: 388.893px 221.71px"
                  >
                    <path
                      d="M405.06,144.72s0-4.12-5.77-9.88-9.47-9.06-9.47-9.06-2.89,9.47,2.47,15.24a101,101,0,0,0,12.77,11.14Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 397.051px 138.97px;
                      "
                      id="elfvj387f8qf7"
                      class="animable"
                    ></path>
                    <path
                      d="M396.69,170.45s0-4.12-5.77-9.89-9.47-9.06-9.47-9.06-2.88,9.47,2.47,15.24a102.63,102.63,0,0,0,12.77,11.15Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 388.683px 164.695px;
                      "
                      id="el6uck0k4d8ww"
                      class="animable"
                    ></path>
                    <path
                      d="M404.69,151.74s.44-4.1,6.78-9.23,10.38-8,10.38-8,1.86,9.75-4.07,14.89a101.51,101.51,0,0,1-13.87,9.71Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 413.018px 146.81px;
                      "
                      id="els1k73v6pkwm"
                      class="animable"
                    ></path>
                    <path
                      d="M394.58,182.12s.87-4,7.71-8.46,11.15-6.88,11.15-6.88.83,9.87-5.58,14.39a100.79,100.79,0,0,1-14.81,8.19Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 403.263px 178.07px;
                      "
                      id="elacnuq3cfj7w"
                      class="animable"
                    ></path>
                    <path
                      d="M396.49,215.23s0-4.12,5.76-9.88,9.47-9.06,9.47-9.06,2.89,9.48-2.47,15.24a100.22,100.22,0,0,1-12.76,11.15Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 404.494px 209.485px;
                      "
                      id="elke0dchxdv8b"
                      class="animable"
                    ></path>
                    <path
                      d="M396.39,223.32s.58-4.08-4.31-10.6S384,202.4,384,202.4s-4.18,9,.29,15.44a101.44,101.44,0,0,0,11.06,12.82Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 389.318px 216.53px;
                      "
                      id="elmdregip223"
                      class="animable"
                    ></path>
                    <path
                      d="M390.7,247.27s0-4.11,5.77-9.88,9.47-9,9.47-9,2.89,9.47-2.47,15.23a101,101,0,0,1-12.77,11.15Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 398.709px 241.58px;
                      "
                      id="elrfduncb97d"
                      class="animable"
                    ></path>
                    <path
                      d="M394.55,269s-1-4,3.09-11,6.87-11.15,6.87-11.15,5.18,8.44,1.47,15.37a101.21,101.21,0,0,1-9.54,14Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 400.88px 261.535px;
                      "
                      id="elg2q7s2kmsc"
                      class="animable"
                    ></path>
                    <path
                      d="M405.58,143.7A116.75,116.75,0,0,1,404,156.48c-2.28,11.78-8,19.38-10.26,30s4.27,24.35,2.52,38-7.08,21.26-5.58,30.38,9.5,33.8,9.5,33.8"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 397.999px 216.18px;
                      "
                      id="elcoe7ibq2ylg"
                      class="animable"
                    ></path>
                    <line
                      x1="404.64"
                      y1="154.48"
                      x2="410.85"
                      y2="149.7"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 407.745px 152.09px;
                      "
                      id="elb78k3sa3egi"
                      class="animable"
                    ></line>
                    <line
                      x1="404.92"
                      y1="148.99"
                      x2="402.33"
                      y2="144.36"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 403.625px 146.675px;
                      "
                      id="elfmrzq20mxqu"
                      class="animable"
                    ></line>
                    <path
                      d="M394.09,186s5.2-3.91,10.55-7.81"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 399.365px 182.095px;
                      "
                      id="elh0iele2zrmm"
                      class="animable"
                    ></path>
                    <path
                      d="M396.69,177.84s-4.34-8.5-9.25-14.14"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 392.065px 170.77px;
                      "
                      id="elot8t0fj58v"
                      class="animable"
                    ></path>
                    <path
                      d="M396.69,221.09a64.58,64.58,0,0,1,11.15-14.88"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 402.265px 213.65px;
                      "
                      id="elqhnfo4ehq6"
                      class="animable"
                    ></path>
                    <path
                      d="M395.68,226.87A73.4,73.4,0,0,0,390,216.16c-3-4.48-3.48-5.06-3.48-5.06"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 391.1px 218.985px;
                      "
                      id="el5oil6bv2whk"
                      class="animable"
                    ></path>
                    <path
                      d="M390.7,254.85a69.58,69.58,0,0,1,4.69-9.48,61.55,61.55,0,0,1,6.06-8"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 396.075px 246.11px;
                      "
                      id="elrcbuhm3xo9f"
                      class="animable"
                    ></path>
                    <path
                      d="M396.43,276.17a17.62,17.62,0,0,1,1.56-9.12c2.17-4.19,4.62-9.69,5.64-11.85"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 399.989px 265.685px;
                      "
                      id="ell26nykxujtn"
                      class="animable"
                    ></path>
                    <path
                      d="M367.3,199.74s-.45-2.57,2.54-6.82,5-6.72,5-6.72,2.83,5.62.1,9.82a64.16,64.16,0,0,1-6.8,8.36Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 371.702px 195.29px;
                      "
                      id="elbqt4mvffknv"
                      class="animable"
                    ></path>
                    <path
                      d="M375.34,215s-.45-2.59,2.54-6.83,5-6.71,5-6.71,2.83,5.63.1,9.82a63.5,63.5,0,0,1-6.8,8.36Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 379.742px 210.55px;
                      "
                      id="elbr8jz4badnl"
                      class="animable"
                    ></path>
                    <path
                      d="M368.29,204.1s-.7-2.52-5.25-5-7.38-3.9-7.38-3.9-.11,6.27,4.18,8.9a64.14,64.14,0,0,0,9.75,4.58Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 362.625px 201.94px;
                      "
                      id="elw4qt83q8g1"
                      class="animable"
                    ></path>
                    <path
                      d="M377.93,222.05s-1-2.43-5.76-4.46-7.75-3.1-7.75-3.1.55,6.27,5.08,8.41a64.52,64.52,0,0,0,10.18,3.53Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 372.05px 220.46px;
                      "
                      id="elcnq5c0ttoge"
                      class="animable"
                    ></path>
                    <path
                      d="M380.33,243s-.45-2.58-4.69-5.57-6.93-4.66-6.93-4.66-.78,6.27,3.21,9.29a63.9,63.9,0,0,0,9.21,5.57Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 374.889px 240.2px;
                      "
                      id="elug0o5t0bm9"
                      class="animable"
                    ></path>
                    <path
                      d="M381.26,248.09s-.81-2.49,1.55-7.11,4-7.35,4-7.35,3.6,5.16,1.5,9.71a65.19,65.19,0,0,1-5.58,9.24Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 385.047px 243.105px;
                      "
                      id="elclcnuk8piku"
                      class="animable"
                    ></path>
                    <path
                      d="M388.5,268.92s.3-2.6,4.36-5.83,6.65-5,6.65-5,1.14,6.2-2.67,9.46a64.88,64.88,0,0,1-8.87,6.1Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 393.82px 265.87px;
                      "
                      id="elb1vg582to2"
                      class="animable"
                    ></path>
                    <path
                      d="M387.43,262.5s-.45-2.59-4.69-5.58-6.92-4.65-6.92-4.65-.78,6.27,3.2,9.29a64.36,64.36,0,0,0,9.22,5.57Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 381.999px 259.7px;
                      "
                      id="elzy9jugm7yid"
                      class="animable"
                    ></path>
                    <path
                      d="M387.37,276.54s.23-2.62-3.12-6.58-5.52-6.27-5.52-6.27-2.34,5.85.75,9.8a65.37,65.37,0,0,0,7.5,7.75Z"
                      style="
                        fill: rgb(47, 107, 224);
                        transform-origin: 382.627px 272.465px;
                      "
                      id="elhuefkt4xrt"
                      class="animable"
                    ></path>
                    <path
                      d="M366.85,199.14a74,74,0,0,0,2.39,7.85c2.7,7.13,7.1,11.28,9.68,17.7s0,15.73,2.55,24.1,6.75,12.54,6.78,18.45S386,289.48,386,289.48"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 377.55px 244.31px;
                      "
                      id="ela7d152xvo7"
                      class="animable"
                    ></path>
                    <line
                      x1="368.62"
                      y1="205.81"
                      x2="364.21"
                      y2="203.5"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 366.415px 204.655px;
                      "
                      id="elpk104th5ktk"
                      class="animable"
                    ></line>
                    <line
                      x1="367.84"
                      y1="202.4"
                      x2="368.97"
                      y2="199.22"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 368.405px 200.81px;
                      "
                      id="elrjruyb8a2wr"
                      class="animable"
                    ></line>
                    <path
                      d="M378.66,224.42s-3.69-1.88-7.46-3.75"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 374.93px 222.545px;
                      "
                      id="el5czl5qrtul9"
                      class="animable"
                    ></path>
                    <path
                      d="M376.14,219.64a52.85,52.85,0,0,1,4.27-9.86"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 378.275px 214.71px;
                      "
                      id="elsmhv53xxt4f"
                      class="animable"
                    ></path>
                    <path
                      d="M380.84,246.72a41.15,41.15,0,0,0-8.6-8.12"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 376.54px 242.66px;
                      "
                      id="elyl2crle140d"
                      class="animable"
                    ></path>
                    <path
                      d="M382.1,250.24a45.85,45.85,0,0,1,2.37-7.31c1.39-3.15,1.63-3.56,1.63-3.56"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 384.1px 244.805px;
                      "
                      id="elway736uodv"
                      class="animable"
                    ></path>
                    <path
                      d="M388.06,271.7a38.47,38.47,0,0,1,4.74-4.92,25.47,25.47,0,0,0,3.08-2.69"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 391.97px 267.895px;
                      "
                      id="elxhj3k1svsso"
                      class="animable"
                    ></path>
                    <path
                      d="M388.25,267.24a44.87,44.87,0,0,0-4-5.43,39,39,0,0,0-4.67-4.33"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 383.915px 262.36px;
                      "
                      id="eln1i4fuht3a"
                      class="animable"
                    ></path>
                    <path
                      d="M387,281.24a11.28,11.28,0,0,0-2-5.57c-1.82-2.4-4-5.58-4.82-6.83"
                      style="
                        fill: none;
                        stroke: rgb(38, 50, 56);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        stroke-width: 1.17749px;
                        transform-origin: 383.59px 275.04px;
                      "
                      id="elwgvbpctxu4b"
                      class="animable"
                    ></path>
                    <path
                      d="M406.18,317.64H379.92a2,2,0,0,1-2-1.95l-3.77-29.06h37.72l-3.77,29.06A2,2,0,0,1,406.18,317.64Z"
                      style="
                        fill: rgb(38, 50, 56);
                        transform-origin: 393.01px 302.135px;
                      "
                      id="elgdvjvlwnp9l"
                      class="animable"
                    ></path>
                    <path
                      d="M411.57,291.54H374.19a2.94,2.94,0,0,1-2.95-2.92h0a3,3,0,0,1,2.94-2.95h37.39a2.94,2.94,0,0,1,2.95,2.94h0A2.94,2.94,0,0,1,411.57,291.54Z"
                      style="
                        fill: rgb(38, 50, 56);
                        transform-origin: 392.88px 288.605px;
                      "
                      id="el49dy4bvwjaf"
                      class="animable"
                    ></path>
                  </g>
                </g>
                <g
                  id="freepik--Coins--inject-4"
                  class="animable"
                  style="transform-origin: 302.969px 147.776px"
                >
                  <path
                    d="M327.3,175.31a36.31,36.31,0,0,1-28.13,35.37l-.11,0c-.95.21-1.9.4-2.88.54a37.08,37.08,0,0,1-5.19.37A36.31,36.31,0,0,1,289.65,139c.33,0,.68,0,1,0a1.72,1.72,0,0,1,.32,0,2.3,2.3,0,0,1,.37,0,35.75,35.75,0,0,1,14.22,3.05A36.31,36.31,0,0,1,327.3,175.31Z"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 291.055px 175.291px;
                    "
                    id="el5q49c9v8ldb"
                    class="animable"
                  ></path>
                  <g id="elhpp2eb1odo9">
                    <path
                      d="M305.58,142.06c-41.93-2.18-42.13,44.37-42.13,44.37s-6.4-15.19,4.05-32c5.84-9.41,16-13.56,23.86-15.4A35.75,35.75,0,0,1,305.58,142.06Z"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.4;
                        transform-origin: 283.545px 162.73px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M327.3,175.31a36.31,36.31,0,0,1-28.24,35.4,35.71,35.71,0,0,1-8.07.91A36.31,36.31,0,0,1,289.65,139c.44,0,.89,0,1.34,0A36.32,36.32,0,0,1,327.3,175.31Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 290.652px 175.313px;
                    "
                    id="elo3zejyu1b4"
                    class="animable"
                  ></path>
                  <path
                    d="M327.3,175.31a36.31,36.31,0,0,1-28.24,35.4c-.44,0-.89,0-1.34,0A36.32,36.32,0,0,1,289.65,139c.44,0,.89,0,1.34,0A36.32,36.32,0,0,1,327.3,175.31Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 294.398px 174.855px;
                    "
                    id="elvu0d737u12"
                    class="animable"
                  ></path>
                  <path
                    d="M303.5,198l.94,3.84-6.28,1.53-.94-3.83c-5.62,0-10.13-3.65-11.57-9.51l-.43-1.74,6.93-2.51.44,1.81c.87,3.56,3.22,5.27,6.15,4.55a4.5,4.5,0,0,0,3.59-5.83c-.84-3.42-4.76-5.56-9.3-7.77-5.37-2.6-10.91-5.9-12.43-12.11-1.48-6.06.38-11.11,5.17-13.61l-.94-3.84,6.27-1.53.94,3.83c5.45-.08,9.51,3.51,11,9.71l.31,1.26-6.92,2.51-.35-1.39c-.92-3.77-2.86-5.36-5.44-4.73s-3.65,2.6-2.9,5.66c.84,3.42,4.81,5.48,9.3,7.78,5.31,2.69,10.91,5.9,12.43,12.1C311,190.15,308.56,195.47,303.5,198Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 295.013px 175.41px;
                    "
                    id="el2e31wgba411"
                    class="animable"
                  ></path>
                  <g id="elw3twbtghcra">
                    <path
                      d="M303.5,198l.94,3.84-6.28,1.53-.94-3.83c-5.62,0-10.13-3.65-11.57-9.51l-.43-1.74,6.93-2.51.44,1.81c.87,3.56,3.22,5.27,6.15,4.55a4.5,4.5,0,0,0,3.59-5.83c-.84-3.42-4.76-5.56-9.3-7.77-5.37-2.6-10.91-5.9-12.43-12.11-1.48-6.06.38-11.11,5.17-13.61l-.94-3.84,6.27-1.53.94,3.83c5.45-.08,9.51,3.51,11,9.71l.31,1.26-6.92,2.51-.35-1.39c-.92-3.77-2.86-5.36-5.44-4.73s-3.65,2.6-2.9,5.66c.84,3.42,4.81,5.48,9.3,7.78,5.31,2.69,10.91,5.9,12.43,12.1C311,190.15,308.56,195.47,303.5,198Z"
                      style="opacity: 0.3; transform-origin: 295.013px 175.41px"
                      class="animable"
                    ></path>
                  </g>
                  <line
                    x1="272.43"
                    y1="206.53"
                    x2="280.1"
                    y2="206.18"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 276.265px 206.355px;
                    "
                    id="elcp89hcy7zx9"
                    class="animable"
                  ></line>
                  <line
                    x1="268.74"
                    y1="203.98"
                    x2="275.43"
                    y2="203.41"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 272.085px 203.695px;
                    "
                    id="elivuseofa28o"
                    class="animable"
                  ></line>
                  <line
                    x1="265.74"
                    y1="201.29"
                    x2="272.43"
                    y2="200.72"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 269.085px 201.005px;
                    "
                    id="el00dkx8nn915h"
                    class="animable"
                  ></line>
                  <line
                    x1="264.82"
                    y1="150.13"
                    x2="270.55"
                    y2="150.13"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 267.685px 150.13px;
                    "
                    id="el2f7mljgwks5"
                    class="animable"
                  ></line>
                  <line
                    x1="267.65"
                    y1="147.49"
                    x2="273.36"
                    y2="147.23"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 270.505px 147.36px;
                    "
                    id="ellw3map41lph"
                    class="animable"
                  ></line>
                  <line
                    x1="271.23"
                    y1="144.84"
                    x2="277.25"
                    y2="144.27"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 274.24px 144.555px;
                    "
                    id="elyba3qrv8mce"
                    class="animable"
                  ></line>
                  <g id="eljo5bwcgfra9">
                    <path
                      d="M299.17,210.68l-.11,0a35.71,35.71,0,0,1-8.07.91A36.31,36.31,0,0,1,289.65,139c.33,0,.68,0,1,0-8.85,2.79-23.7,9.92-27.67,24.79C257.37,184.92,270,208.91,299.17,210.68Z"
                      style="opacity: 0.3; transform-origin: 276.99px 175.295px"
                      class="animable"
                    ></path>
                  </g>
                  <g id="el60x1qpd8vie">
                    <path
                      d="M327.3,175.31a36.31,36.31,0,0,1-28.13,35.37l-.11,0c-.95.21-1.9.4-2.88.54l9.4-69.19A36.31,36.31,0,0,1,327.3,175.31Z"
                      style="opacity: 0.1; transform-origin: 311.74px 176.625px"
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M347.9,118.15a22,22,0,0,1-26.31,7.64l-.13-.06a22.63,22.63,0,0,1-2-1,19.63,19.63,0,0,1-2.12-1.32,22,22,0,0,1,24.19-36.63l.49.31c.23.13.45.29.68.45s.67.49,1,.75A22,22,0,0,1,347.9,118.15Z"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 330.314px 105.682px;
                    "
                    id="elgju244ygl2"
                    class="animable"
                  ></path>
                  <g id="eljkqg3h4vq8">
                    <path
                      d="M314.76,118.45s-3.59-10.57,1.18-18.35c6-9.75,15.19-14.2,27.7-11.8,0,0-6.78-4.58-18.13-1.33S308.07,105.84,314.76,118.45Z"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.4;
                        transform-origin: 327.81px 102.127px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M347.9,118.16a22,22,0,0,1-26.31,7.62,21.55,21.55,0,0,1-4.3-2.36A22,22,0,0,1,342,87.1c.22.13.45.28.67.44A22,22,0,0,1,347.9,118.16Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 330.303px 105.698px;
                    "
                    id="elvbyklhjph3r"
                    class="animable"
                  ></path>
                  <path
                    d="M347.9,118.16a22,22,0,0,1-26.31,7.62l-.67-.44A22,22,0,0,1,342,87.1c.22.13.45.28.67.44A22,22,0,0,1,347.9,118.16Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 331.879px 106.48px;
                    "
                    id="elwcp9f1vzdk"
                    class="animable"
                  ></path>
                  <path
                    d="M328.21,121.08l-.87,2.22-3.64-1.43.87-2.23a7.12,7.12,0,0,1-2.4-8.73l.4-1,4.3,1.17-.41,1.06c-.81,2.06-.26,3.72,1.44,4.39a2.72,2.72,0,0,0,3.81-1.63c.78-2-.41-4.41-1.88-7.08-1.74-3.16-3.32-6.72-1.91-10.32s4.07-5.36,7.3-4.92l.88-2.22,3.63,1.43L338.86,94c2.72,1.86,3.47,5,2.06,8.65l-.29.72-4.3-1.17.32-.81c.86-2.18.46-3.65-1-4.24s-2.71,0-3.41,1.79c-.78,2,.47,4.38,1.88,7.08,1.69,3.19,3.33,6.73,1.92,10.32A7.09,7.09,0,0,1,328.21,121.08Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 331.583px 106.83px;
                    "
                    id="elcztlwou9tzd"
                    class="animable"
                  ></path>
                  <g id="elhz4yhs95kq">
                    <path
                      d="M328.21,121.08l-.87,2.22-3.64-1.43.87-2.23a7.12,7.12,0,0,1-2.4-8.73l.4-1,4.3,1.17-.41,1.06c-.81,2.06-.26,3.72,1.44,4.39a2.72,2.72,0,0,0,3.81-1.63c.78-2-.41-4.41-1.88-7.08-1.74-3.16-3.32-6.72-1.91-10.32s4.07-5.36,7.3-4.92l.88-2.22,3.63,1.43L338.86,94c2.72,1.86,3.47,5,2.06,8.65l-.29.72-4.3-1.17.32-.81c.86-2.18.46-3.65-1-4.24s-2.71,0-3.41,1.79c-.78,2,.47,4.38,1.88,7.08,1.69,3.19,3.33,6.73,1.92,10.32A7.09,7.09,0,0,1,328.21,121.08Z"
                      style="opacity: 0.3; transform-origin: 331.583px 106.83px"
                      class="animable"
                    ></path>
                  </g>
                  <g id="el2la7e6o6qdw">
                    <path
                      d="M341.48,86.79c-6-1.07-13.6-1.66-17.7,1-7.53,4.83-12.22,10.92-12.15,20.79s9.29,16.79,9.29,16.79l.54.39a21.66,21.66,0,0,1-4.17-2.31,22,22,0,0,1,24.19-36.63Z"
                      style="
                        opacity: 0.3;
                        transform-origin: 325.061px 104.863px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <g id="elhywg7i9x837">
                    <path
                      d="M347.9,118.15a22,22,0,0,1-26.31,7.64l-.13-.06a22.63,22.63,0,0,1-2-1L343.64,88.3A22,22,0,0,1,347.9,118.15Z"
                      style="
                        opacity: 0.1;
                        transform-origin: 335.684px 107.869px;
                      "
                      class="animable"
                    ></path>
                  </g>
                </g>
                <g
                  id="freepik--Character--inject-4"
                  class="animable"
                  style="transform-origin: 259.872px 295.137px"
                >
                  <path
                    d="M336.58,399.76a132,132,0,0,1-32.84,28.35,116.06,116.06,0,0,1-12,6.26c-28.41,12.77-60.79,13.6-91.07,6.26C137,425.2,103.22,369,98.93,306.61c-1.15-16.86-3.73-35.07,3.1-51.11s20.64-26,36.26-32.92c11.34-5.05,23.59-8.44,34.72-11.21a89.31,89.31,0,0,1,60.09,6.2c13.09,6.21,22.41,17,33.42,26.09q1.75,1.44,3.52,2.87a329.9,329.9,0,0,0,35,24.61c18.51,11.29,37.46,21.94,49.29,40.9C373.2,342.26,357.28,375.16,336.58,399.76Z"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 230.114px 327.021px;
                    "
                    id="elou96bw81s58"
                    class="animable"
                  ></path>
                  <g id="el7jqbzrlf0wg">
                    <path
                      d="M336.58,399.76a132,132,0,0,1-32.84,28.35,116.06,116.06,0,0,1-12,6.26c-28.41,12.77-60.79,13.6-91.07,6.26C137,425.2,103.22,369,98.93,306.61c-1.15-16.86-3.73-35.07,3.1-51.11s20.64-26,36.26-32.92c11.34-5.05,23.59-8.44,34.72-11.21a89.31,89.31,0,0,1,60.09,6.2c13.09,6.21,22.41,17,33.42,26.09q1.75,1.44,3.52,2.87a329.9,329.9,0,0,0,35,24.61c18.51,11.29,37.46,21.94,49.29,40.9C373.2,342.26,357.28,375.16,336.58,399.76Z"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.4;
                        transform-origin: 230.114px 327.021px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M270,246.53q-1.77-1.42-3.52-2.88c-11-9.11-20.34-19.87-33.42-26.08a89.37,89.37,0,0,0-60.09-6.2c-26.47,6.57-59.3,16.69-71,44.14-6.83,16-4.25,34.24-3.09,51.1C103.22,369,137,425.2,200.67,440.63c34.64,8.4,72,6.1,103.07-12.52a132,132,0,0,0,32.84-28.35c20.7-24.6,36.62-57.5,17.77-87.72-11.82-19-30.77-29.61-49.29-40.9A329.9,329.9,0,0,1,270,246.53Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 230.088px 327.023px;
                    "
                    id="elelaz1322j1w"
                    class="animable"
                  ></path>
                  <path
                    d="M173.55,357.78A91.57,91.57,0,0,0,158.18,364"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 165.865px 360.89px;
                    "
                    id="elkc1ksutuiv7"
                    class="animable"
                  ></path>
                  <path
                    d="M206,351.6a164,164,0,0,0-23,3.65"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 194.5px 353.425px;
                    "
                    id="elaxk00micqh5"
                    class="animable"
                  ></path>
                  <path
                    d="M199.25,350s-29.88-17.38-65.94-3.45"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 166.28px 345.571px;
                    "
                    id="eln64w6bbisbj"
                    class="animable"
                  ></path>
                  <g id="elxzmc576mie">
                    <path
                      d="M336.58,399.76a132,132,0,0,1-32.84,28.35,116.06,116.06,0,0,1-12,6.26,93.7,93.7,0,0,1-9.57-3.51c-18.66-8-34.32-21.53-49.62-34.87-9.23-8-18.61-16.2-29.66-21.47a68.22,68.22,0,0,0-47.84-3.94,53.73,53.73,0,0,1,31.64-17.41c-7.9-7.19-18.89-10.21-29.57-10.4s-16.56,1.12-27,3.46c3.49-6.42,12.75-12.23,20.06-12.06a181.67,181.67,0,0,1-20.78-1.47c-9.43-1.52-13.65-12.62-15.06-22.06-3.9-26,.05-53.69,14.24-75.79a77.15,77.15,0,0,1,9.7-12.27c11.34-5.05,23.59-8.44,34.72-11.21a89.31,89.31,0,0,1,60.09,6.2c13.09,6.21,22.41,17,33.42,26.09q1.75,1.44,3.52,2.87a329.9,329.9,0,0,0,35,24.61c18.51,11.29,37.46,21.94,49.29,40.9C373.2,342.26,357.28,375.16,336.58,399.76Z"
                      style="
                        opacity: 0.1;
                        transform-origin: 237.831px 321.567px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M347,399a193.35,193.35,0,0,0,17.57,13c11.89.8,6.56,15.17,6.56,15.17l-10.18,6.55S343.52,419.87,334,415.38,347,399,347,399Z"
                    style="
                      fill: rgb(255, 255, 255);
                      transform-origin: 351.987px 416.36px;
                    "
                    id="elzxnzdbg64t"
                    class="animable"
                  ></path>
                  <g id="elc8q1vyuv7h">
                    <path
                      d="M262.51,247.7s-1.63-16.13-4.23-20.66L218.8,219v-3.06s.91-5.32-7.89-8.65-27.14,1-31.87,4.58-3,9.74-3,9.74S160.14,229.64,153,232.7a182.44,182.44,0,0,0-17.07,9s-3.24,37.08-2.6,45.69-.52,16.71.22,21.48,1.32,7.48,6.32,11.54,9.55,10,22.66,4.11l-1.83-1.45L247,294Z"
                      style="
                        opacity: 0.3;
                        transform-origin: 197.878px 266.547px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M262.51,247.7s-1.63-16.13-4.23-20.66L218.8,219v-3.06s.91-5.32-7.89-8.65-27.14,1-31.87,4.58-3,9.74-3,9.74S160.14,229.64,153,232.7a182.44,182.44,0,0,0-17.07,9s-3.24,37.08-2.6,45.69-.52,16.71.22,21.48,1.32,7.48,6.32,11.54,9.55,10,22.66,4.11l-1.83-1.45L247,294Z"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 197.878px 266.547px;
                    "
                    id="el331cgd07qoe"
                    class="animable"
                  ></path>
                  <g id="els5m3jbfvybk">
                    <path
                      d="M262.51,247.7s-1.63-16.13-4.23-20.66L218.8,219v-3.06s.91-5.32-7.89-8.65-27.14,1-31.87,4.58-3,9.74-3,9.74S160.14,229.64,153,232.7a182.44,182.44,0,0,0-17.07,9s-3.24,37.08-2.6,45.69-.52,16.71.22,21.48,1.32,7.48,6.32,11.54,9.55,10,22.66,4.11l-1.83-1.45L247,294Z"
                      style="
                        opacity: 0.05;
                        transform-origin: 197.878px 266.547px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <g id="eliltcyi3vh3">
                    <path
                      d="M258.8,229H242.28a6.57,6.57,0,0,0-6.47,5.49l-8.14,49s-11.21.93-19.15,6.72-27.47-35.52-58.33-35.54c0,0,8.88,20.3,10.85,36.5,0,0-19.29-2.1-22.49,5.45s1.45,21.7,9.61,16.22S164,298.22,169.45,304s29.8,8.89,29.8,8.89L236.72,294l14.53-6.83Z"
                      style="
                        opacity: 0.1;
                        transform-origin: 198.166px 271.536px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <g id="elemty4362x3">
                    <path
                      d="M206.35,204.71s9.38,2.6,10.9,5.75,1.36,11.12,1.36,11.12a38.5,38.5,0,0,1-10.09,13.88l-7-13.88.63,15.74s-13.28-2.21-18.92-6.45l-5.63-4.25s-2.43-8-2.08-10.71S186,207.7,186,207.7Z"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.4;
                        transform-origin: 197.05px 221.015px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M362.59,447.42c-.26-.57,3.85-7.83.63-15.22s-8.35-4.54-8.35-4.54-14.61-9.3-20.75-12.24a104.7,104.7,0,0,1-12.75-7.58l-6.49,8.74,6,18.56Z"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 339.669px 427.63px;
                    "
                    id="el6yfwumvelk5"
                    class="animable"
                  ></path>
                  <path
                    d="M354.34,427.21l18.76,11.46s13.19-8.46,19.48-14.3S413,406.09,413,406.09l9-12.11s-6.24-7.72-16.64-1.52c-13.44,8-29.83,19.62-40.76,19.51,0,0,2.15,13.43-10.19,15.24"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 388.17px 414.36px;
                    "
                    id="eluajx7c5303c"
                    class="animable"
                  ></path>
                  <path
                    d="M203.17,156.23s8.51,12.88,10.06,17.05,2.8,10.34,2.49,13.31-3.43,18.88-5,19.34l-1.51,1.71,1,6.74s-6.66,11.51-26.24,3.07c0,0-.76-13.44-3.14-17.66s-5.94.8-9.51-11.39,7.8-25,7.8-25Z"
                    style="
                      fill: rgb(255, 255, 255);
                      transform-origin: 193.197px 188.386px;
                    "
                    id="el2tunguuglv8"
                    class="animable"
                  ></path>
                  <g id="el0x6tva8wrto">
                    <path
                      d="M413.83,390.22s-9.65,1.68-12.78,7.39-9.14,16.32-11.19,21.45-19,17.52-19,17.52l2.25,2.09s13.8-8.53,22.13-16.64,26.7-28.51,26.7-28.51S420.62,392.21,413.83,390.22Z"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.5;
                        transform-origin: 396.4px 414.445px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <g id="elaho1x0h2vic">
                    <path
                      d="M317.27,413.37l-2.39,3.21,7.34,15.22,40.37,14.51,1.87-5.91s.62-6-13.55-10.56-22.81-7.57-26.84-10.7S317.27,413.37,317.27,413.37Z"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.5;
                        transform-origin: 339.672px 429.84px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M184,300.33s-12.36-1.86-18.13,8.25-5.18,14.49-5.18,14.49,8.17,7.42,19.4,7S207.67,316.79,213,312.6s18.77-16.18,27.94-13.7l1-8.67s-.9-3.89-8.74-2.84-10.92,5.52-13.45,5.52-4.92-2.43-10,6.25C209.74,299.16,191.91,302.32,184,300.33Z"
                    style="
                      fill: rgb(255, 255, 255);
                      transform-origin: 201.306px 308.649px;
                    "
                    id="el1ft2zf20muv"
                    class="animable"
                  ></path>
                  <path
                    d="M200.06,174.75a35.8,35.8,0,0,0,1.12,4.79,5.58,5.58,0,0,0,1,2.11c.94,1,2.46,1.26,3.85,1.47a1.05,1.05,0,0,1,.62.26,1,1,0,0,1,.15.6,8,8,0,0,1-1.36,4.42"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 203.431px 181.575px;
                    "
                    id="ellpcjhn913k"
                    class="animable"
                  ></path>
                  <g id="elaihymt0rana">
                    <path
                      d="M325.56,403l-8.29,10.33s9.1,8.41,18.52,11.53,25.91,9,28.67,13.53L363.4,433s-25.79-15.82-31.71-19.6A13.66,13.66,0,0,1,325.56,403Z"
                      style="
                        opacity: 0.1;
                        transform-origin: 340.865px 420.695px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <g id="el23hbpg5myv2">
                    <path
                      d="M354.34,427.21l16.51,9.37S379,431,383,426.89s8.93-8.67,12-17.88c4.19-12.73,16.19-18.79,16.19-18.79s-16.69,7.86-25.38,14-17.16,8.74-22.08,8.32C363.68,412.57,367.63,425.07,354.34,427.21Z"
                      style="opacity: 0.1; transform-origin: 382.765px 413.4px"
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M194.24,190.67s6.49,5.09,13.23,3.18C207.47,193.85,201.11,206.45,194.24,190.67Z"
                    style="
                      fill: rgb(255, 255, 255);
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 200.855px 194.642px;
                    "
                    id="elv477jqqdya"
                    class="animable"
                  ></path>
                  <path
                    d="M194,179.67c.19,1.4-.24,2.61-.94,2.71s-1.43-1-1.62-2.37.23-2.61.94-2.71S193.85,178.27,194,179.67Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 192.72px 179.84px;
                    "
                    id="el3bhj7lbesp4"
                    class="animable"
                  ></path>
                  <path
                    d="M208.13,177.12c.18,1.41-.24,2.62-.94,2.71s-1.44-1-1.62-2.36.23-2.62.94-2.71S207.94,175.72,208.13,177.12Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 206.85px 177.295px;
                    "
                    id="elp1cx8iehb0s"
                    class="animable"
                  ></path>
                  <path
                    d="M193,173.73s-3.78-1.95-6.47,2.23"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 189.765px 174.618px;
                    "
                    id="elgemghtna51d"
                    class="animable"
                  ></path>
                  <path
                    d="M202.69,171.13s4-3.43,6.31,0"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 205.845px 170.368px;
                    "
                    id="el6tz4jiie49y"
                    class="animable"
                  ></path>
                  <path
                    d="M180.16,186.6s-2.43-3.59-5-3.48-3.59,3.71-2.67,7.19,4.75,8.34,7.65,7.88a6.44,6.44,0,0,0,4.17-2.32"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 178.234px 190.669px;
                    "
                    id="ellld9oaozl5"
                    class="animable"
                  ></path>
                  <path
                    d="M203.46,159.13c2.73,2.27,5.62,4.74,7,8.08,1.22,2.84,1.49,6,2.61,8.87,1.19,3.07,2.49,6.28,2.59,9.63a40.79,40.79,0,0,1-1.21,9.19,50.65,50.65,0,0,1-1.85,7.69c-1.35,3.59-3.84,5.8-7.18,7.5-2.43,1.24-6.48,3-9.11,1.34-1.53-1-3.83-3.86-5.74-6.49"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 203.115px 185.619px;
                    "
                    id="el38qezc4vsf"
                    class="animable"
                  ></path>
                  <path
                    d="M180.16,198.19s4.3,10.54,3.26,17.95"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 181.871px 207.165px;
                    "
                    id="eloqtwktxr15"
                    class="animable"
                  ></path>
                  <path
                    d="M179.06,184.83a4.14,4.14,0,0,0,1.66-3.71c-.16-1.62-1.28-3.12-1.06-4.74a8.73,8.73,0,0,1,.86-2.22,9.31,9.31,0,0,0-.52-8.42s13.66.64,20.06-3.43,11.28-14.48,7.41-19.45-11.41,1.81-17,2.56S165,149.34,161.83,157s1.66,10.41,1.81,17.72.15,21.16,11.08,19.89c0,0-4.09-5.29-1.25-10.74C175.4,181.58,179.06,184.83,179.06,184.83Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 184.8px 167.873px;
                    "
                    id="el2j5q6lowfwd"
                    class="animable"
                  ></path>
                  <path
                    d="M205.85,151.5a17.14,17.14,0,0,1,6.54,12c.61,5.48,10.33,16.13,3.32,22.2,0,0-1.63-6.45-2.59-9.63s-2.19-9-3.8-11-10.15-9-10.15-9Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 208.705px 168.6px;
                    "
                    id="el7x8712k09r4"
                    class="animable"
                  ></path>
                  <path
                    d="M181.42,215.91s6.74,4.44,19.26,4.62l1.47,16.79s-14.08-1.07-24.55-10.7c0,0-2.83-7.49-2.08-10.71S183,209.5,183,209.5"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 188.772px 223.41px;
                    "
                    id="elbokq28iytic"
                    class="animable"
                  ></path>
                  <path
                    d="M209.74,208.4l.4,6a24.82,24.82,0,0,1-9.46,6.15l7.84,14.93s9.35-7.28,10.28-14.93c0,0,.08-7.83-1.55-10.07s-7.57-3.29-7.57-3.29"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 209.74px 221.335px;
                    "
                    id="el3vbxr3y3xx3"
                    class="animable"
                  ></path>
                  <g id="ele2ggqke1iy7">
                    <path
                      d="M171.8,162c1-2.51,3.74-4,6.41-4.4s5.41-.12,8.11-.19a20.82,20.82,0,0,0,10.6-3,14.17,14.17,0,0,0,6.4-8.76"
                      style="
                        fill: none;
                        stroke: rgb(255, 255, 255);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        opacity: 0.8;
                        transform-origin: 187.56px 153.825px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <g id="ell27jogqqxmf">
                    <path
                      d="M165.35,161.51c1.69-5.13,7.17-8.3,12.56-8.75,2.67-.23,5.37.08,8-.23s5.43-1.41,6.82-3.7"
                      style="
                        fill: none;
                        stroke: rgb(255, 255, 255);
                        stroke-linecap: round;
                        stroke-linejoin: round;
                        opacity: 0.8;
                        transform-origin: 179.04px 155.17px;
                      "
                      class="animable"
                    ></path>
                  </g>
                  <path
                    d="M162.51,324.52c-13,6.74-17.62-.2-24.86-5.94s-2.49-16-4-32.45,2.25-44.43,2.25-44.43,3.24-.74,12.23-6.24,27.91-13.88,27.91-13.88"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 154.666px 274.438px;
                    "
                    id="elzczjrqcc2"
                    class="animable"
                  ></path>
                  <path
                    d="M175.39,299.62s-2.06-6.53-7.88-6.7-15,2.45-21.54,14.82"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 160.68px 300.326px;
                    "
                    id="eluj6gufxmpze"
                    class="animable"
                  ></path>
                  <path
                    d="M166.18,292.53a3.22,3.22,0,0,1-1.05-.25,25.31,25.31,0,0,0-14.94-.68"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 158.185px 291.658px;
                    "
                    id="el8627uv4jeyn"
                    class="animable"
                  ></path>
                  <path
                    d="M146.84,250.63c10.59,11.7,15.75,27.18,20.64,42.18"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 157.16px 271.72px;
                    "
                    id="elqc71o8z3qml"
                    class="animable"
                  ></path>
                  <path
                    d="M218.8,219s19.2,4.58,28.58,5.67,10.9,2.4,10.9,2.4l1.75,6.54"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 239.415px 226.305px;
                    "
                    id="elnr7kpugq0i"
                    class="animable"
                  ></path>
                  <polygon
                    points="308.27 230.38 242.88 232.89 237.54 300.14 248.74 300.14 305.52 287.43 312.66 231.99 308.27 230.38"
                    style="
                      fill: rgb(255, 255, 255);
                      transform-origin: 275.1px 265.26px;
                    "
                    id="elqjrq0h87h5"
                    class="animable"
                  ></polygon>
                  <polygon
                    points="240.94 302.13 312.46 233.58 305.52 287.43 240.94 302.13"
                    style="
                      fill: rgb(47, 107, 224);
                      transform-origin: 276.7px 267.855px;
                    "
                    id="el2wkf8z92elu"
                    class="animable"
                  ></polygon>
                  <g id="el7u6glelvofx">
                    <polygon
                      points="240.94 302.13 312.46 233.58 305.52 287.43 240.94 302.13"
                      style="
                        fill: rgb(255, 255, 255);
                        opacity: 0.7;
                        transform-origin: 276.7px 267.855px;
                      "
                      class="animable"
                    ></polygon>
                  </g>
                  <polygon
                    points="240.94 300.33 246.66 235.46 312.66 231.99 305.52 287.43 240.94 300.33"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 276.8px 266.16px;
                    "
                    id="ellk12w2aiyqq"
                    class="animable"
                  ></polygon>
                  <path
                    d="M284.84,263.42c-.44,3.95-2.55,6.95-4.71,6.71s-3.54-3.65-3.1-7.6,2.56-6.95,4.72-6.71S285.29,259.47,284.84,263.42Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 280.937px 262.975px;
                    "
                    id="eldqr45n66vt6"
                    class="animable"
                  ></path>
                  <path
                    d="M305.52,287.43s30.22-.86,39.67,5.38,15,37.53,13.69,54.15-12.71,54.76-12.71,54.76L324.33,377s-10.76-39.12-4.89-54.44c0,0-29,1.63-40.42-1.95,0,0-10.23-16.75-27.77-22.29Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 305.16px 344.555px;
                    "
                    id="el96yr4jw4hdp"
                    class="animable"
                  ></path>
                  <path
                    d="M350.78,402.37a149.82,149.82,0,0,1-20.78-24c-10.57-16.33-25.18-32-40.21-44.23-12.11-9.82-14.85-25.82-29.79-33.85-24.75-13.27-46.15,13.1-54.15,18.44S184,330.12,184,330.12s5.06,19,19.92,21.25,29.89-8,37-3.05,12.37,27.05,35.85,40c19.19,10.58,45.34,20.9,58.83,27.86,3,1.56,5.24,3.2,6.94,4.13,4.66,2.54,9.64,5.53,14.34,8.49l16.19,9.86s16.25-9.61,22.49-18.47c.83-1.19,2.56-4.36,4.18-7.42,3.53-6.69,8-12.69,14.19-16,4.28-2.31,8.09-3,8-3.24-.41-.76-8.17-4.73-13.44-2.56-11.16,4.61-33.47,22.32-44,21A78.83,78.83,0,0,1,350.78,402.37Z"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 302.966px 367.629px;
                    "
                    id="elbs6qjb0xrk"
                    class="animable"
                  ></path>
                  <path
                    d="M350.78,402.37c-9.73,3.93-13.65,10-15.15,13.82-13.49-7-39.64-17.28-58.83-27.86-23.48-13-28.7-35.06-35.85-40s-22.19,5.34-37,3.05S184,330.11,184,330.11s13.86-6,21.86-11.34,26.45-31.72,54.14-18.44c15.29,7.33,17.68,24,29.79,33.85,15,12.19,29.64,27.9,40.21,44.23A147,147,0,0,0,350.78,402.37Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 267.39px 356.412px;
                    "
                    id="el5nle1mjk977"
                    class="animable"
                  ></path>
                  <path
                    d="M277.66,317.59s-7.49-15.22-19.5-18.17"
                    style="
                      fill: none;
                      stroke: rgb(255, 255, 255);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 267.91px 308.505px;
                    "
                    id="el2vurp01bnlt"
                    class="animable"
                  ></path>
                  <path
                    d="M238.4,287.29c-8.26-1.8-17.33,7.33-21.15,5.62-2.84-1.27-7.51,6.25-7.51,6.25a125.11,125.11,0,0,1-23.22,1.17c-11.13-.71-16.4,1.46-20.34,7.41l-5.5,14.57s14.51,13,30,5.43,36.07-30.41,50.25-30.33"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 200.805px 308.581px;
                    "
                    id="el7de3jcfspqk"
                    class="animable"
                  ></path>
                  <path
                    d="M353.71,426.82s13.56,1.11,10.38-15.09"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 359.142px 419.283px;
                    "
                    id="elmokswzkjiv"
                    class="animable"
                  ></path>
                  <path
                    d="M417.61,401.72c-1,2-13.43,12.58-24.14,21.83,0-.14,3-5.79,4.24-8.2,2.85-5.32,10.21-19.91,24.22-21.83C423.92,394.72,417.61,401.72,417.61,401.72Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 407.894px 408.535px;
                    "
                    id="elnjhvukuevd"
                    class="animable"
                  ></path>
                  <path
                    d="M412.28,390.33c-14.17,2.82-17.39,21-22.42,28.73s-19,18.24-19,18.24"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 391.57px 413.815px;
                    "
                    id="elqefk7znvrag"
                    class="animable"
                  ></path>
                  <path
                    d="M363.22,432.66c2.31,4.22,1.22,9.12,0,12.19a25.45,25.45,0,0,1-2.22,4.37c-20.06-3.3-40.12-14.08-40.12-14.08l-5.17-16-.83-2.56,7.79-10.49"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 339.727px 427.655px;
                    "
                    id="elfaf492z9qmi"
                    class="animable"
                  ></path>
                  <path
                    d="M363.22,444.85a25.45,25.45,0,0,1-2.22,4.37c-20.06-3.3-40.12-14.08-40.12-14.08l-5.17-16c2.59,3.38,7.39,8.33,14.59,10.36C340.07,432.27,358.1,435.51,363.22,444.85Z"
                    style="
                      fill: rgb(38, 50, 56);
                      transform-origin: 339.465px 434.18px;
                    "
                    id="elio7o9b28vxj"
                    class="animable"
                  ></path>
                  <polyline
                    points="237.54 297.8 242.88 232.89 308.27 230.38 312.66 231.99"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 275.1px 264.09px;
                    "
                    id="el2w2fwzveqmf"
                    class="animable"
                  ></polyline>
                  <path
                    d="M364,419.06s8.94,1.6,15.81-4,17.94-18.27,17.94-18.27"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 380.875px 408.031px;
                    "
                    id="elhy7cynsgp5h"
                    class="animable"
                  ></path>
                  <path
                    d="M373.1,411s-2.16,3.25,0,9.77"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 372.62px 415.885px;
                    "
                    id="eljuklw263d58"
                    class="animable"
                  ></path>
                  <path
                    d="M379.44,407.89a13.31,13.31,0,0,0,.81,9.77"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 379.559px 412.775px;
                    "
                    id="elu36ryhcpfc"
                    class="animable"
                  ></path>
                  <path
                    d="M387,403.51s-1.81,5.43,0,10.22"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-linecap: round;
                      stroke-linejoin: round;
                      transform-origin: 386.598px 408.62px;
                    "
                    id="el06z0gmv2pbh"
                    class="animable"
                  ></path>
                  <path
                    d="M324.44,404.74l-7.17,8.63s7.22,7.89,17.41,11.17,28.39,8.85,29.9,14.78"
                    style="
                      fill: none;
                      stroke: rgb(38, 50, 56);
                      stroke-miterlimit: 10;
                      transform-origin: 340.925px 422.03px;
                    "
                    id="el2a2fppplwpu"
                    class="animable"
                  ></path>
                </g>
                <defs>
                  <filter id="active" height="200%">
                    <feMorphology
                      in="SourceAlpha"
                      result="DILATED"
                      operator="dilate"
                      radius="2"
                    ></feMorphology>
                    <feFlood
                      flood-color="#32DFEC"
                      flood-opacity="1"
                      result="PINK"
                    ></feFlood>
                    <feComposite
                      in="PINK"
                      in2="DILATED"
                      operator="in"
                      result="OUTLINE"
                    ></feComposite>
                    <feMerge>
                      <feMergeNode in="OUTLINE"></feMergeNode>
                      <feMergeNode in="SourceGraphic"></feMergeNode>
                    </feMerge>
                  </filter>
                  <filter id="hover" height="200%">
                    <feMorphology
                      in="SourceAlpha"
                      result="DILATED"
                      operator="dilate"
                      radius="2"
                    ></feMorphology>
                    <feFlood
                      flood-color="#ff0000"
                      flood-opacity="0.5"
                      result="PINK"
                    ></feFlood>
                    <feComposite
                      in="PINK"
                      in2="DILATED"
                      operator="in"
                      result="OUTLINE"
                    ></feComposite>
                    <feMerge>
                      <feMergeNode in="OUTLINE"></feMergeNode>
                      <feMergeNode in="SourceGraphic"></feMergeNode>
                    </feMerge>
                    <feColorMatrix
                      type="matrix"
                      values="0   0   0   0   0                0   1   0   0   0                0   0   0   0   0                0   0   0   1   0 "
                    ></feColorMatrix>
                  </filter>
                </defs>
              </svg>
            </div>

            <p
              class="mt-6 text-xl pl-4 lg:p-auto text-slate-300 sm:mt-8 sm:text-2xl leading-relaxed"
            >
              Experience modern banking with cutting-edge security and seamless
              transactions.
            </p>
          </div>
          <div class="mt-10 flex items-center justify-start pl-4 gap-4">
            <a
              href="/login"
              class="btn-gradient px-8 py-3 rounded-lg text-lg font-medium text-white transition-all duration-200"
            >
              Log In
            </a>
            <a
              href="https://github.com/ITx-prash/securebank-cpp"
              target="_blank"
              rel="noopener noreferrer"
              class="bg-slate-700/50 hover:bg-slate-600/50 px-8 py-3 rounded-lg text-lg font-medium text-slate-300 hover:text-blue-400 transition-all duration-200"
            >
              Learn More
            </a>
          </div>
        </div>

        <!-- Right Column - Signup Form -->
        <div
          id="signup"
          class="bg-slate-800/30 backdrop-blur-sm 2xl:mr-36 rounded-xl p-6 sm:p-8 border border-slate-700/30 lg:max-w-xl lg:ml-auto"
        >
          <h2 class="text-2xl font-bold text-slate-100 mb-6 flex items-center">
            <span
              class="w-8 h-8 bg-gradient-to-r from-slate-700 to-slate-800 rounded-lg flex items-center justify-center mr-3 border border-slate-600"
            >
              <svg
                class="w-5 h-5 text-slate-300"
                fill="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"
                />
              </svg>
            </span>
            Create Your Account
          </h2>
          <form id="signupForm" class="space-y-6">
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label
                  for="firstName"
                  class="block text-sm font-medium text-slate-300 mb-2"
                  >First Name</label
                >
                <input
                  type="text"
                  id="firstName"
                  name="firstName"
                  required
                  class="input-dark block w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
                />
              </div>
              <div>
                <label
                  for="lastName"
                  class="block text-sm font-medium text-slate-300 mb-2"
                  >Last Name</label
                >
                <input
                  type="text"
                  id="lastName"
                  name="lastName"
                  required
                  class="input-dark block w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
                />
              </div>
            </div>

            <div>
              <label
                for="email"
                class="block text-sm font-medium text-slate-300 mb-2"
                >Email Address</label
              >
              <input
                type="email"
                id="email"
                name="email"
                required
                class="input-dark block w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
              />
            </div>

            <div>
              <label
                for="phone"
                class="block text-sm font-medium text-slate-300 mb-2"
                >Phone Number</label
              >
              <input
                type="tel"
                id="phone"
                name="phone"
                required
                class="input-dark block w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
              />
            </div>

            <div>
              <label
                for="password"
                class="block text-sm font-medium text-slate-300 mb-2"
                >Password</label
              >
              <input
                type="password"
                id="password"
                name="password"
                required
                class="input-dark block w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
              />
            </div>

            <div>
              <label
                for="confirmPassword"
                class="block text-sm font-medium text-slate-300 mb-2"
                >Confirm Password</label
              >
              <input
                type="password"
                id="confirmPassword"
                name="confirmPassword"
                required
                class="input-dark block w-full px-4 py-3 rounded-lg border-0 focus:ring-2 focus:ring-blue-500 focus:outline-none transition-all"
              />
            </div>

            <div class="flex items-center">
              <input
                id="terms"
                name="terms"
                type="checkbox"
                required
                class="h-4 w-4 text-blue-500 focus:ring-blue-500 bg-slate-700 border-slate-600 rounded"
              />
              <label for="terms" class="ml-2 block text-sm text-slate-300">
                I agree to the
                <a
                  href="#"
                  class="text-blue-400 hover:text-blue-300 transition-colors"
                  >Terms and Conditions</a
                >
              </label>
            </div>

            <button
              type="submit"
              class="btn-gradient w-full py-3 px-6 rounded-lg font-medium text-white transition-all duration-200 flex items-center justify-center"
            >
              <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                <path
                  d="M12,2A10,10 0 0,1 22,12A10,10 0 0,1 12,22A10,10 0 0,1 2,12A10,10 0 0,1 12,2M12,4A8,8 0 0,0 4,12A8,8 0 0,0 12,20A8,8 0 0,0 20,12A8,8 0 0,0 12,4M11,16.5L6.5,12L7.91,10.59L11,13.67L16.59,8.09L18,9.5L11,16.5Z"
                />
              </svg>
              Create Account
            </button>
          </form>

          <p class="mt-6 text-center text-sm text-slate-400">
            Already have an account?
            <a
              href="/login"
              class="text-blue-400 hover:text-blue-300 font-medium transition-colors"
              >Log in</a
            >
          </p>
        </div>
      </div>
    </main>

    <!-- JavaScript Modules -->
    <script src="/js/utils.js"></script>
    <script src="/js/api.js"></script>
    <script src="/js/auth.js"></script>
    <script>
      // Registration form handler
      document.addEventListener("DOMContentLoaded", function () {
        const signupForm = document.getElementById("signupForm");

        signupForm.addEventListener("submit", async function (e) {
          e.preventDefault();

          // Get form data
          const formData = new FormData(signupForm);
          const firstName = formData.get("firstName").trim();
          const lastName = formData.get("lastName").trim();
          const email = formData.get("email").trim();
          const phone = formData.get("phone").trim();
          const password = formData.get("password");
          const confirmPassword = formData.get("confirmPassword");
          const terms = formData.get("terms");

          // Validation
          const validationRules = {
            firstName: { required: true, minLength: 2, label: "First Name" },
            lastName: { required: true, minLength: 2, label: "Last Name" },
            email: {
              required: true,
              pattern: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
              message: "Please enter a valid email address",
              label: "Email",
            },
            phone: { required: true, minLength: 10, label: "Phone Number" },
            password: { required: true, minLength: 6, label: "Password" },
          };

          const userData = { firstName, lastName, email, phone, password };
          const validation = window.utils.validateForm(
            userData,
            validationRules
          );

          // Additional validation
          const errors = validation.errors;

          if (password !== confirmPassword) {
            errors.confirmPassword = "Passwords do not match";
          }

          if (!terms) {
            errors.terms = "You must agree to the terms and conditions";
          }

          if (Object.keys(errors).length > 0) {
            window.utils.displayFormErrors(errors);
            return;
          }

          // Clear any existing errors
          window.utils.displayFormErrors({});

          // Disable submit button during registration
          const submitBtn = signupForm.querySelector('button[type="submit"]');
          const originalText = submitBtn.textContent;
          submitBtn.disabled = true;
          submitBtn.textContent = "Creating Account...";

          try {
            // Register user
            const success = await window.auth.register({
              name: `${firstName} ${lastName}`,
              email,
              phone,
              password,
            });

            if (success) {
              signupForm.reset();
              window.location.href = "/login?registered=true";
            }
          } catch (error) {
            console.error("Registration error:", error);
          } finally {
            // Re-enable submit button
            submitBtn.disabled = false;
            submitBtn.textContent = originalText;
          }
        });
      });
    </script>
  </body>
</html>
